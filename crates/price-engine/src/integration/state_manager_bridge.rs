//! State Manager Bridge
//!
//! 状态管理器集成桥接，用于连接价格引擎与状态管理器

use async_trait::async_trait;
use dashmap::DashMap;
use solana_sdk::pubkey::Pubkey;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

use crate::calculator::traits::PriceCalculator;
use crate::calculator::{RaydiumClmmPriceCalculator, MeteoraeDlmmPriceCalculator};
use crate::types::{DexType, TokenPair, PriceEngineError, PriceEngineResult};
use state_manager::core::dex_manager::UnifiedPoolManager;
use state_manager::core::types::{DexProtocol, PoolType, PoolId};
use state_manager::dex::raydium::clmm::pool::RaydiumClmmPoolState;
use state_manager::dex::meteora::dlmm::pool::MeteoraLbPairState;

/// 状态管理器桥接器
/// 
/// 负责将状态管理器的池状态数据提供给价格计算器
#[derive(Debug)]
pub struct StateManagerBridge {
    /// 统一池管理器
    pool_manager: Arc<RwLock<Option<Arc<UnifiedPoolManager>>>>,
    /// 已注册的价格计算器
    calculators: Arc<DashMap<DexType, Box<dyn PriceCalculator>>>,
    /// 池ID到代币对的映射
    pool_token_pairs: Arc<RwLock<DashMap<Pubkey, TokenPair>>>,
    /// 代币对到池ID的映射
    token_pair_pools: Arc<RwLock<DashMap<TokenPair, Vec<Pubkey>>>>,
}

impl StateManagerBridge {
    /// 创建新的状态管理器桥接器
    pub fn new() -> Self {
        Self {
            pool_manager: Arc::new(RwLock::new(None)),
            calculators: Arc::new(DashMap::new()),
            pool_token_pairs: Arc::new(RwLock::new(DashMap::new())),
            token_pair_pools: Arc::new(RwLock::new(DashMap::new())),
        }
    }

    /// 设置统一池管理器
    pub async fn set_pool_manager(&self, manager: Arc<UnifiedPoolManager>) {
        info!("设置统一池管理器");
        *self.pool_manager.write().await = Some(manager);
        
        // 初始化所有计算器
        self.initialize_calculators().await;
    }

    /// 初始化价格计算器
    async fn initialize_calculators(&self) {
        info!("初始化价格计算器");
        
        // 创建并注册 Raydium CLMM 计算器
        let raydium_calculator = Box::new(RaydiumClmmPriceCalculator::new(None));
        if let Some(manager) = self.pool_manager.read().await.as_ref() {
            if let Some(raydium_calc) = raydium_calculator.as_any().downcast_ref::<RaydiumClmmPriceCalculator>() {
                raydium_calc.set_pool_manager(Arc::clone(manager)).await;
            }
        }
        self.calculators.insert(DexType::RaydiumClmm, raydium_calculator);

        // 创建并注册 Meteora DLMM 计算器
        let meteora_calculator = Box::new(MeteoraeDlmmPriceCalculator::new(None));
        if let Some(manager) = self.pool_manager.read().await.as_ref() {
            if let Some(meteora_calc) = meteora_calculator.as_any().downcast_ref::<MeteoraeDlmmPriceCalculator>() {
                meteora_calc.set_pool_manager(Arc::clone(manager)).await;
            }
        }
        self.calculators.insert(DexType::MeteoraDefi, meteora_calculator);

        info!("价格计算器初始化完成，共注册 {} 个计算器", self.calculators.len());
    }

    /// 注册池状态更新
    pub async fn register_pool(&self, pool_id: &Pubkey, token_pair: TokenPair, dex_type: DexType) {
        debug!("注册池: {} -> {} ({})", pool_id, token_pair, dex_type);
        
        // 更新池到代币对的映射
        self.pool_token_pairs.write().await.insert(*pool_id, token_pair.clone());
        
        // 更新代币对到池的映射
        {
            let token_pair_pools = self.token_pair_pools.write().await;
            token_pair_pools.entry(token_pair.clone()).or_insert_with(Vec::new).push(*pool_id);
            
            // 同时注册反向代币对
            let reverse_pair = token_pair.reverse();
            token_pair_pools.entry(reverse_pair).or_insert_with(Vec::new).push(*pool_id);
        }

        // 通知相应的计算器
        if let Some(calculator) = self.calculators.get(&dex_type) {
            match dex_type {
                DexType::RaydiumClmm => {
                    if let Some(raydium_calc) = calculator.as_any().downcast_ref::<RaydiumClmmPriceCalculator>() {
                        // 这里需要实际的池状态数据，暂时创建一个占位符
                        // 在实际集成中，这里应该从 UnifiedPoolManager 获取池状态
                        debug!("通知 Raydium 计算器池状态更新: {}", pool_id);
                    }
                },
                DexType::MeteoraDefi => {
                    if let Some(meteora_calc) = calculator.as_any().downcast_ref::<MeteoraeDlmmPriceCalculator>() {
                        debug!("通知 Meteora 计算器池状态更新: {}", pool_id);
                    }
                },
                _ => {
                    warn!("不支持的 DEX 类型: {:?}", dex_type);
                }
            }
        }
    }

    /// 处理 Raydium 池状态更新
    pub async fn handle_raydium_pool_update(&self, pool_id: &Pubkey, pool_state: &RaydiumClmmPoolState) -> PriceEngineResult<()> {
        debug!("处理 Raydium 池状态更新: {}", pool_id);
        
        let token_pair = TokenPair::new(pool_state.token_mint_0, pool_state.token_mint_1);
        self.register_pool(pool_id, token_pair, DexType::RaydiumClmm).await;

        // 通知 Raydium 计算器
        if let Some(calculator) = self.calculators.get(&DexType::RaydiumClmm) {
            if let Some(raydium_calc) = calculator.as_any().downcast_ref::<RaydiumClmmPriceCalculator>() {
                raydium_calc.handle_pool_state_update(pool_id, pool_state).await;
            }
        }

        Ok(())
    }

    /// 处理 Meteora 池状态更新
    pub async fn handle_meteora_pool_update(&self, pool_id: &Pubkey, pool_state: &MeteoraLbPairState) -> PriceEngineResult<()> {
        debug!("处理 Meteora 池状态更新: {}", pool_id);
        
        let token_pair = TokenPair::new(pool_state.token_x_mint, pool_state.token_y_mint);
        self.register_pool(pool_id, token_pair, DexType::MeteoraDefi).await;

        // 通知 Meteora 计算器
        if let Some(calculator) = self.calculators.get(&DexType::MeteoraDefi) {
            if let Some(meteora_calc) = calculator.as_any().downcast_ref::<MeteoraeDlmmPriceCalculator>() {
                meteora_calc.handle_pool_state_update(pool_id, pool_state).await;
            }
        }

        Ok(())
    }

    /// 查找支持指定代币对的池
    pub async fn find_pools_for_pair(&self, token_pair: &TokenPair) -> Vec<(Pubkey, DexType)> {
        let token_pair_pools = self.token_pair_pools.read().await;
        let pool_token_pairs = self.pool_token_pairs.read().await;
        
        let mut result = Vec::new();
        
        // 查找直接匹配的池
        if let Some(pools) = token_pair_pools.get(token_pair) {
            for pool_id in pools.iter() {
                // 根据池类型确定 DEX 类型（这里需要更好的方法来识别）
                // 暂时通过池管理器的注册信息来判断
                if let Some(_manager) = self.pool_manager.read().await.as_ref() {
                    // 这里应该查询 UnifiedPoolManager 来确定池的类型
                    // 暂时使用简化的逻辑
                    result.push((*pool_id, DexType::RaydiumClmm)); // 占位符
                } else {
                    // 即使没有池管理器，也应该返回已注册的池，使用默认的 DEX 类型
                    result.push((*pool_id, DexType::RaydiumClmm)); // 默认类型
                }
            }
        }
        
        result
    }

    /// 获取指定池的代币对
    pub async fn get_token_pair_for_pool(&self, pool_id: &Pubkey) -> Option<TokenPair> {
        self.pool_token_pairs.read().await.get(pool_id).map(|pair| pair.clone())
    }

    /// 获取价格计算器
    pub fn get_calculator(&self, dex_type: &DexType) -> Option<dashmap::mapref::one::Ref<DexType, Box<dyn PriceCalculator>>> {
        self.calculators.get(dex_type)
    }

    /// 获取所有支持的代币对
    pub async fn get_all_supported_pairs(&self) -> Vec<TokenPair> {
        let token_pair_pools = self.token_pair_pools.read().await;
        token_pair_pools.iter().map(|entry| entry.key().clone()).collect()
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> BridgeStats {
        let pool_count = self.pool_token_pairs.read().await.len();
        let pair_count = self.token_pair_pools.read().await.len();
        let calculator_count = self.calculators.len();
        
        BridgeStats {
            total_pools: pool_count,
            total_token_pairs: pair_count,
            total_calculators: calculator_count,
            is_manager_connected: self.pool_manager.read().await.is_some(),
        }
    }

    /// 健康检查
    pub async fn health_check(&self) -> BridgeHealthStatus {
        let manager_connected = self.pool_manager.read().await.is_some();
        let calculator_count = self.calculators.len();
        
        let is_healthy = manager_connected && calculator_count > 0;
        
        BridgeHealthStatus {
            is_healthy,
            manager_connected,
            calculator_count,
            last_check: chrono::Utc::now(),
        }
    }
}

impl Default for StateManagerBridge {
    fn default() -> Self {
        Self::new()
    }
}

/// 桥接器统计信息
#[derive(Debug, Clone)]
pub struct BridgeStats {
    /// 总池数量
    pub total_pools: usize,
    /// 总代币对数量
    pub total_token_pairs: usize,
    /// 总计算器数量
    pub total_calculators: usize,
    /// 管理器是否连接
    pub is_manager_connected: bool,
}

/// 桥接器健康状态
#[derive(Debug, Clone)]
pub struct BridgeHealthStatus {
    /// 是否健康
    pub is_healthy: bool,
    /// 管理器是否连接
    pub manager_connected: bool,
    /// 计算器数量
    pub calculator_count: usize,
    /// 最后检查时间
    pub last_check: chrono::DateTime<chrono::Utc>,
}


#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[tokio::test]
    async fn test_bridge_creation() {
        let bridge = StateManagerBridge::new();
        let stats = bridge.get_stats().await;
        
        assert_eq!(stats.total_pools, 0);
        assert_eq!(stats.total_token_pairs, 0);
        assert!(!stats.is_manager_connected);
    }

    #[tokio::test]
    async fn test_pool_registration() {
        let bridge = StateManagerBridge::new();
        
        let pool_id = Pubkey::from_str("11111111111111111111111111111112").unwrap();
        let token_a = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let token_b = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap();
        let token_pair = TokenPair::new(token_a, token_b);
        
        bridge.register_pool(&pool_id, token_pair.clone(), DexType::RaydiumClmm).await;
        
        let found_pools = bridge.find_pools_for_pair(&token_pair).await;
        assert!(!found_pools.is_empty());
        
        let found_pair = bridge.get_token_pair_for_pool(&pool_id).await;
        assert_eq!(found_pair, Some(token_pair));
    }

    #[tokio::test]
    async fn test_health_check() {
        let bridge = StateManagerBridge::new();
        let health = bridge.health_check().await;
        
        assert!(!health.is_healthy);
        assert!(!health.manager_connected);
        assert_eq!(health.calculator_count, 0);
    }
}