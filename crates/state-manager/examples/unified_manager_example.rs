//! 统一池管理器使用示例
//!
//! 展示如何使用重构后的 DexPoolManager 来管理多个 DEX 的池

use state_manager::{
    DexPoolManager, DexPoolManagerConfig, PoolManager, PoolState,
    SwapDirection, DexProtocol, PoolType,
};
use state_manager::dex::raydium::clmm::{RaydiumClmmPoolManager, RaydiumClmmPoolState};
use state_manager::dex::meteora::dlmm::{MeteoraLbPoolManager, MeteoraLbPairState};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 统一池管理器示例");

    // 1. 创建统一管理器
    let dex_manager = DexPoolManager::with_default_config();
    println!("✅ 创建统一管理器");

    // 2. 创建示例 Raydium CLMM 池
    let raydium_pool = create_example_raydium_pool()?;
    let raydium_manager = RaydiumClmmPoolManager::new(raydium_pool);
    
    // 添加到统一管理器
    dex_manager.add_raydium_pool(raydium_manager)?;
    println!("✅ 添加 Raydium CLMM 池");

    // 3. 创建示例 Meteora DLMM 池
    let meteora_pool = create_example_meteora_pool()?;
    let meteora_manager = MeteoraLbPoolManager::new(meteora_pool);
    
    // 添加到统一管理器
    dex_manager.add_meteora_pool(meteora_manager)?;
    println!("✅ 添加 Meteora DLMM 池");

    // 4. 获取统计信息
    let stats = dex_manager.get_global_stats();
    println!("\n📊 全局统计:");
    println!("  总池数量: {}", stats.total_pools);
    println!("  活跃池数量: {}", stats.active_pools);
    println!("  按协议分组: {:?}", stats.pools_by_protocol);
    println!("  按类型分组: {:?}", stats.pools_by_type);

    // 5. 查找代币对
    let usdc = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?; // USDC
    let sol = Pubkey::from_str("So11111111111111111111111111111111111111112")?;   // SOL
    
    let pools = dex_manager.find_pools_for_tokens(&usdc, &sol);
    println!("\n🔍 找到 USDC/SOL 池数量: {}", pools.len());

    // 6. 估算交换
    let input_amount = 1_000_000; // 1 USDC (6 decimals)
    match dex_manager.estimate_swap(&usdc, &sol, input_amount, Some(5.0)) {
        Ok(estimations) => {
            println!("\n💱 交换估算结果:");
            for (i, estimation) in estimations.iter().enumerate() {
                println!("  池 {}: {} USDC -> {} SOL (价格影响: {:.2}%)", 
                    i + 1, 
                    estimation.input_amount, 
                    estimation.output_amount,
                    estimation.price_impact
                );
            }
        }
        Err(e) => {
            println!("❌ 交换估算失败: {}", e);
        }
    }

    // 7. 获取最佳报价
    match dex_manager.get_best_swap_quote(&usdc, &sol, input_amount, Some(5.0)) {
        Ok(best_quote) => {
            println!("\n🏆 最佳报价:");
            println!("  输入: {} USDC", best_quote.input_amount);
            println!("  输出: {} SOL", best_quote.output_amount);
            println!("  价格影响: {:.2}%", best_quote.price_impact);
            println!("  费用: {}", best_quote.fee_amount);
        }
        Err(e) => {
            println!("❌ 获取最佳报价失败: {}", e);
        }
    }

    // 8. 获取流动性排行
    let top_pools = dex_manager.get_top_pools_by_liquidity(5);
    println!("\n🏅 流动性排行榜 (前5名):");
    for (i, (pool_id, liquidity)) in top_pools.iter().enumerate() {
        println!("  {}. {} - {} (流动性: {})", 
            i + 1, 
            pool_id.protocol, 
            pool_id.pool_type,
            liquidity
        );
    }

    println!("\n🎉 示例完成!");
    Ok(())
}

/// 创建示例 Raydium CLMM 池
fn create_example_raydium_pool() -> Result<RaydiumClmmPoolState, Box<dyn std::error::Error>> {
    let pool_address = Pubkey::from_str("61R1ndXxvsWXXkWSyNkCxnzwd3zUNB8Q2ibmkiLPC8ht")?;
    let usdc = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?;
    let sol = Pubkey::from_str("So11111111111111111111111111111111111111112")?;

    Ok(RaydiumClmmPoolState {
        pool_id: pool_address,
        amm_config: Pubkey::default(),
        owner: Pubkey::default(),
        token_mint_0: usdc,
        token_mint_1: sol,
        token_vault_0: Pubkey::default(),
        token_vault_1: Pubkey::default(),
        observation_key: Pubkey::default(),
        mint_decimals_0: 6,  // USDC
        mint_decimals_1: 9,  // SOL
        tick_spacing: 64,
        liquidity: 1_000_000_000_000, // 示例流动性
        sqrt_price_x64: 1844674407370955161600, // 示例价格
        tick_current: 0,
        fee_growth_global_0_x64: 0,
        fee_growth_global_1_x64: 0,
        protocol_fees_token_0: 0,
        protocol_fees_token_1: 0,
        swap_in_amount_token_0: 0,
        swap_out_amount_token_1: 0,
        swap_in_amount_token_1: 0,
        swap_out_amount_token_0: 0,
        status: 1,
        sqrt_price_limit_x64: 0,
        padding: [0; 4],
        reward_infos: Vec::new(),
        tick_array_bitmap: [0; 16],
        total_fees_token_0: 0,
        total_fees_token_1: 0,
        total_fees_claimed_token_0: 0,
        total_fees_claimed_token_1: 0,
        fund_fees_token_0: 0,
        fund_fees_token_1: 0,
        open_time: 0,
        recent_epoch: 0,
        fee_rate: 2500, // 0.25%
        active: true,
    })
}

/// 创建示例 Meteora DLMM 池
fn create_example_meteora_pool() -> Result<MeteoraLbPairState, Box<dyn std::error::Error>> {
    let pool_address = Pubkey::from_str("Ak6mDLQCNYSVkQnWUJqWojzFVKdGgKkKwNdqZhcE6Zx")?;
    let usdc = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?;
    let sol = Pubkey::from_str("So11111111111111111111111111111111111111112")?;

    Ok(MeteoraLbPairState::new(
        pool_address,
        usdc,
        sol,
        6, // USDC decimals
        9, // SOL decimals
        8388608, // active_id
        25, // bin_step (0.25%)
    ))
}
