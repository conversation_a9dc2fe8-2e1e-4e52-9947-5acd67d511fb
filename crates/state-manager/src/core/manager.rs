//! 池管理器抽象
//!
//! 定义池管理器的通用接口和基础实现

use super::types::*;
use super::pool::PoolState;
use std::collections::HashSet;

/// 池管理器 trait - 基于实际 DEX 实现的通用接口
///
/// 这个 trait 定义了所有池管理器必须提供的核心功能，
/// 基于 Raydium CLMM 和 Meteora DLMM 的实际实现抽象而来
pub trait PoolManager: Send + Sync {
    /// 池状态类型
    type State: PoolState;

    /// 获取池状态引用
    fn get_state(&self) -> &Self::State;

    /// 获取当前价格
    fn get_current_price(&self) -> Result<f64>;

    /// 估算交换输出
    ///
    /// # 参数
    /// * `input_amount` - 输入金额
    /// * `direction` - 交换方向
    /// * `max_price_impact` - 最大价格影响（可选）
    ///
    /// # 返回
    /// 返回交换估算结果
    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        max_price_impact: Option<f64>,
    ) -> Result<SwapEstimation>;

    /// 获取流动性信息
    fn get_liquidity_info(&self) -> LiquidityInfo;

    /// 更新池状态（如果支持）
    fn update_pool_state(&mut self, new_state: Self::State) -> Result<()> {
        // 默认实现：不支持状态更新
        Err(shared::EchoesError::UnsupportedOperation(
            "Pool state update not supported".to_string()
        ))
    }
}


}



/// 池管理器注册表
#[derive(Debug)]
pub struct PoolManagerRegistry {
    /// 注册的池ID
    pool_ids: HashSet<PoolId>,
}

impl PoolManagerRegistry {
    /// 创建新的注册表
    pub fn new() -> Self {
        Self {
            pool_ids: HashSet::new(),
        }
    }

    /// 注册池ID
    pub fn register(&mut self, pool_id: PoolId) {
        self.pool_ids.insert(pool_id);
    }

    /// 检查池是否存在
    pub fn contains(&self, pool_id: &PoolId) -> bool {
        self.pool_ids.contains(pool_id)
    }

    /// 移除池
    pub fn remove(&mut self, pool_id: &PoolId) -> bool {
        self.pool_ids.remove(pool_id)
    }

    /// 获取所有池ID
    pub fn get_all_pool_ids(&self) -> Vec<PoolId> {
        self.pool_ids.iter().cloned().collect()
    }

    /// 按协议过滤
    pub fn get_by_protocol(&self, protocol: DexProtocol) -> Vec<PoolId> {
        self.pool_ids
            .iter()
            .filter(|id| id.protocol == protocol)
            .cloned()
            .collect()
    }

    /// 按池类型过滤
    pub fn get_by_pool_type(&self, pool_type: PoolType) -> Vec<PoolId> {
        self.pool_ids
            .iter()
            .filter(|id| id.pool_type == pool_type)
            .cloned()
            .collect()
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> CacheStats {
        let mut pools_by_protocol = std::collections::HashMap::new();
        let mut pools_by_type = std::collections::HashMap::new();

        for pool_id in &self.pool_ids {
            // 按协议统计
            *pools_by_protocol.entry(pool_id.protocol).or_insert(0) += 1;

            // 按类型统计
            *pools_by_type.entry(pool_id.pool_type).or_insert(0) += 1;
        }

        CacheStats {
            total_pools: self.pool_ids.len(),
            pools_by_protocol,
            pools_by_type,
            active_pools: self.pool_ids.len(), // 简化处理
            total_liquidity_usd: 0.0, // 简化处理
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }
}

impl Default for PoolManagerRegistry {
    fn default() -> Self {
        Self::new()
    }
}
