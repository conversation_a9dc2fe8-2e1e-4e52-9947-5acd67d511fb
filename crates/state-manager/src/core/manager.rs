//! 池管理器抽象
//!
//! 定义池管理器的通用接口和基础实现

use super::types::*;
use super::pool::PoolState;
use std::collections::HashSet;

/// 池管理器 trait
/// 所有类型的池管理器都必须实现这个 trait
pub trait PoolManager: Send + Sync {
    /// 池状态类型
    type State: PoolState;

    /// 获取池状态
    fn get_state(&self) -> &Self::State;

    /// 获取可变池状态
    fn get_state_mut(&mut self) -> &mut Self::State;

    /// 更新池状态
    fn update_state(&mut self, new_state: Self::State) -> PoolResult<()>;

    /// 估算交换
    fn estimate_swap(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        slippage_tolerance: Option<f64>,
    ) -> PoolResult<SwapQuote> {
        let mut quote = self.get_state().estimate_swap_output(input_amount, direction)?;

        // 应用滑点保护
        if let Some(slippage) = slippage_tolerance {
            let slippage_multiplier = 1.0 - (slippage / 100.0);
            quote.minimum_output = Some((quote.output_amount as f64 * slippage_multiplier) as u64);
        }

        Ok(quote)
    }

    /// 获取当前价格
    fn get_current_price(&self, direction: SwapDirection) -> PoolResult<f64> {
        match direction {
            SwapDirection::AToB => self.get_state().calculate_price_a_to_b(),
            SwapDirection::BToA => self.get_state().calculate_price_b_to_a(),
        }
    }

    /// 获取流动性信息
    fn get_liquidity_info(&self) -> PoolResult<LiquiditySnapshot> {
        self.get_state().get_liquidity_snapshot()
    }

    /// 获取价格信息
    fn get_price_info(&self) -> PoolResult<PriceInfo> {
        self.get_state().get_price_info()
    }

    /// 检查池是否健康
    fn is_healthy(&self) -> bool {
        self.get_state().is_active() &&
        self.get_state().token_a_reserve().amount > 0 &&
        self.get_state().token_b_reserve().amount > 0
    }

    /// 获取池标识符
    fn pool_id(&self) -> &PoolId {
        self.get_state().pool_id()
    }

    /// 获取支持的操作
    fn supported_operations(&self) -> Vec<String> {
        vec![
            "swap".to_string(),
            "get_price".to_string(),
            "get_liquidity".to_string(),
        ]
    }

    /// 验证交换参数
    fn validate_swap_params(
        &self,
        input_amount: u64,
        direction: SwapDirection,
    ) -> PoolResult<()> {
        if input_amount == 0 {
            return Err(PoolError::CalculationError("Input amount cannot be zero".to_string()));
        }

        if !self.is_healthy() {
            return Err(PoolError::UnsupportedOperation("Pool is not healthy".to_string()));
        }

        let state = self.get_state();
        let max_input = match direction {
            SwapDirection::AToB => state.token_a_reserve().amount,
            SwapDirection::BToA => state.token_b_reserve().amount,
        };

        if input_amount > max_input {
            return Err(PoolError::CalculationError(
                format!("Input amount {} exceeds available reserve {}", input_amount, max_input)
            ));
        }

        Ok(())
    }
}

/// 通用池管理器实现
#[derive(Debug, Clone)]
pub struct GenericPoolManager<T: PoolState> {
    /// 池状态
    state: T,
}

impl<T: PoolState> GenericPoolManager<T> {
    /// 创建新的池管理器
    pub fn new(state: T) -> Self {
        Self { state }
    }

    /// 从现有状态创建
    pub fn from_state(state: T) -> Self {
        Self { state }
    }
}

impl<T: PoolState> PoolManager for GenericPoolManager<T> {
    type State = T;

    fn get_state(&self) -> &Self::State {
        &self.state
    }

    fn get_state_mut(&mut self) -> &mut Self::State {
        &mut self.state
    }

    fn update_state(&mut self, new_state: Self::State) -> PoolResult<()> {
        self.state = new_state;
        Ok(())
    }
}

/// 池管理器工厂
pub struct PoolManagerFactory;

impl PoolManagerFactory {
    /// 根据池类型创建相应的管理器
    pub fn create_manager(
        pool_id: &PoolId,
        _data: &[u8],
    ) -> PoolResult<()> {
        match (pool_id.protocol, pool_id.pool_type) {
            (DexProtocol::Raydium, PoolType::Clmm) => {
                // 这里会调用 Raydium CLMM 特定的创建逻辑
                Err(PoolError::UnsupportedOperation(
                    "Raydium CLMM manager creation not implemented yet".to_string()
                ))
            }
            (DexProtocol::Meteora, PoolType::Dlmm) => {
                // 这里会调用 Meteora DLMM 特定的创建逻辑
                Err(PoolError::UnsupportedOperation(
                    "Meteora DLMM manager creation not implemented yet".to_string()
                ))
            }
            _ => Err(PoolError::UnsupportedOperation(
                format!("Unsupported pool type: {}:{}", pool_id.protocol, pool_id.pool_type)
            )),
        }?;
        Ok(())
    }
}

/// 池管理器注册表
#[derive(Debug)]
pub struct PoolManagerRegistry {
    /// 注册的池ID
    pool_ids: HashSet<PoolId>,
}

impl PoolManagerRegistry {
    /// 创建新的注册表
    pub fn new() -> Self {
        Self {
            pool_ids: HashSet::new(),
        }
    }

    /// 注册池ID
    pub fn register(&mut self, pool_id: PoolId) {
        self.pool_ids.insert(pool_id);
    }

    /// 检查池是否存在
    pub fn contains(&self, pool_id: &PoolId) -> bool {
        self.pool_ids.contains(pool_id)
    }

    /// 移除池
    pub fn remove(&mut self, pool_id: &PoolId) -> bool {
        self.pool_ids.remove(pool_id)
    }

    /// 获取所有池ID
    pub fn get_all_pool_ids(&self) -> Vec<PoolId> {
        self.pool_ids.iter().cloned().collect()
    }

    /// 按协议过滤
    pub fn get_by_protocol(&self, protocol: DexProtocol) -> Vec<PoolId> {
        self.pool_ids
            .iter()
            .filter(|id| id.protocol == protocol)
            .cloned()
            .collect()
    }

    /// 按池类型过滤
    pub fn get_by_pool_type(&self, pool_type: PoolType) -> Vec<PoolId> {
        self.pool_ids
            .iter()
            .filter(|id| id.pool_type == pool_type)
            .cloned()
            .collect()
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> CacheStats {
        let mut pools_by_protocol = std::collections::HashMap::new();
        let mut pools_by_type = std::collections::HashMap::new();

        for pool_id in &self.pool_ids {
            // 按协议统计
            *pools_by_protocol.entry(pool_id.protocol).or_insert(0) += 1;

            // 按类型统计
            *pools_by_type.entry(pool_id.pool_type).or_insert(0) += 1;
        }

        CacheStats {
            total_pools: self.pool_ids.len(),
            pools_by_protocol,
            pools_by_type,
            active_pools: self.pool_ids.len(), // 简化处理
            total_liquidity_usd: 0.0, // 简化处理
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }
}

impl Default for PoolManagerRegistry {
    fn default() -> Self {
        Self::new()
    }
}
