//! 池状态抽象
//!
//! 定义池状态的通用接口，基于实际 DEX 实现抽象而来

use super::types::*;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use shared::Result;

/// 池状态 trait - 基于实际 DEX 实现的通用接口
///
/// 这个 trait 定义了所有池状态必须提供的核心功能，
/// 基于 Raydium CLMM 和 Meteora DLMM 的实际实现抽象而来
pub trait PoolState: Send + Sync + Clone {
    /// 获取池地址
    fn pool_address(&self) -> Pubkey;

    /// 获取代币对信息 (token_x, token_y, decimals_x, decimals_y)
    fn token_pair(&self) -> (Pubkey, Pubkey, u8, u8);

    /// 获取当前价格 (Y/X 或 Token1/Token0)
    fn current_price(&self) -> Result<f64>;

    /// 检查池是否活跃
    fn is_active(&self) -> bool;

    /// 获取协议类型
    fn protocol(&self) -> DexProtocol;

    /// 获取池类型
    fn pool_type(&self) -> PoolType;
}

/// 池标识符 - 用于唯一标识一个池
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct PoolId {
    /// 协议类型
    pub protocol: DexProtocol,
    /// 池类型
    pub pool_type: PoolType,
    /// 池地址
    pub address: Pubkey,
}

impl PoolId {
    /// 创建新的池标识符
    pub fn new(protocol: DexProtocol, pool_type: PoolType, address: Pubkey) -> Self {
        Self {
            protocol,
            pool_type,
            address,
        }
    }

    /// 从池状态创建池标识符
    pub fn from_pool_state<T: PoolState>(pool_state: &T) -> Self {
        Self::new(
            pool_state.protocol(),
            pool_state.pool_type(),
            pool_state.pool_address(),
        )
    }
}



