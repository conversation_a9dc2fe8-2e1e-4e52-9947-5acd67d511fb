//! 核心类型定义
//!
//! 定义所有 DEX 池管理系统的通用类型和枚举

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::fmt;
use shared::Result;

/// DEX 协议类型
#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DexProtocol {
    /// Raydium DEX
    Raydium,
    /// Meteora DEX
    Meteora,
    /// Orca DEX
    Orca,
    /// Jupiter DEX
    Jupiter,
    /// Serum DEX
    Serum,
}

impl fmt::Display for DexProtocol {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DexProtocol::Raydium => write!(f, "Raydium"),
            DexProtocol::Meteora => write!(f, "Meteora"),
            DexProtocol::Orca => write!(f, "Orca"),
            DexProtocol::Jupiter => write!(f, "Jupiter"),
            DexProtocol::Serum => write!(f, "Serum"),
        }
    }
}

/// 池类型
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PoolType {
    /// 恒定乘积做市商 (Constant Product Market Maker)
    Cpmm,
    /// 集中流动性做市商 (Concentrated Liquidity Market Maker)
    Clmm,
    /// 动态流动性做市商 (Dynamic Liquidity Market Maker)
    Dlmm,
    /// 稳定币池 (Stable Pool)
    Stable,
    /// 加权池 (Weighted Pool)
    Weighted,
}

impl fmt::Display for PoolType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PoolType::Cpmm => write!(f, "CPMM"),
            PoolType::Clmm => write!(f, "CLMM"),
            PoolType::Dlmm => write!(f, "DLMM"),
            PoolType::Stable => write!(f, "Stable"),
            PoolType::Weighted => write!(f, "Weighted"),
        }
    }
}

/// 池标识符
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct PoolId {
    /// DEX 协议
    pub protocol: DexProtocol,
    /// 池类型
    pub pool_type: PoolType,
    /// 池地址
    pub address: Pubkey,
}

impl PoolId {
    /// 创建新的池标识符
    pub fn new(protocol: DexProtocol, pool_type: PoolType, address: Pubkey) -> Self {
        Self {
            protocol,
            pool_type,
            address,
        }
    }
}

impl fmt::Display for PoolId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}:{}:{}", self.protocol, self.pool_type, self.address)
    }
}

/// 代币储备信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenReserve {
    /// 代币铸币地址
    pub mint: Pubkey,
    /// 代币数量
    pub amount: u64,
    /// 小数位数
    pub decimals: u8,
    /// 代币符号（可选）
    pub symbol: Option<String>,
    /// 代币名称（可选）
    pub name: Option<String>,
}

impl TokenReserve {
    /// 创建新的代币储备
    pub fn new(mint: Pubkey, amount: u64, decimals: u8) -> Self {
        Self {
            mint,
            amount,
            decimals,
            symbol: None,
            name: None,
        }
    }

    /// 设置代币符号
    pub fn with_symbol(mut self, symbol: String) -> Self {
        self.symbol = Some(symbol);
        self
    }

    /// 设置代币名称
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// 获取格式化的数量（考虑小数位）
    pub fn formatted_amount(&self) -> f64 {
        self.amount as f64 / 10_f64.powi(self.decimals as i32)
    }
}

/// 交换方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SwapDirection {
    /// A -> B (或 Token0 -> Token1, X -> Y)
    AToB,
    /// B -> A (或 Token1 -> Token0, Y -> X)
    BToA,
}

/// 统一的交换估算结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapEstimation {
    /// 输入金额
    pub input_amount: u64,
    /// 预期输出金额
    pub output_amount: u64,
    /// 最小输出金额（考虑滑点）
    pub minimum_output: Option<u64>,
    /// 价格影响（百分比）
    pub price_impact: f64,
    /// 费用金额
    pub fee_amount: u64,
    /// 交换后的价格
    pub price_after: f64,
    /// 交换方向
    pub direction: SwapDirection,
}

/// 统一的流动性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityInfo {
    /// 总流动性
    pub total_liquidity: u128,
    /// 活跃区间数量（tick数组或bin数量）
    pub active_ranges: usize,
    /// 价格范围 (最小价格, 最大价格)
    pub price_range: (f64, f64),
    /// 流动性分布 (价格点, X代币流动性, Y代币流动性)
    pub distribution: Vec<(f64, u128, u128)>,
}

impl SwapDirection {
    /// 反转交换方向
    pub fn reverse(self) -> Self {
        match self {
            SwapDirection::AToB => SwapDirection::BToA,
            SwapDirection::BToA => SwapDirection::AToB,
        }
    }
}

/// 交换估算结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapQuote {
    /// 输入代币
    pub input_token: Pubkey,
    /// 输出代币
    pub output_token: Pubkey,
    /// 输入金额
    pub input_amount: u64,
    /// 预期输出金额
    pub output_amount: u64,
    /// 费用金额
    pub fee_amount: u64,
    /// 价格影响（百分比）
    pub price_impact: f64,
    /// 交换方向
    pub direction: SwapDirection,
    /// 最小输出金额（考虑滑点）
    pub minimum_output: Option<u64>,
    /// 路由信息（如果是多跳交换）
    pub route: Vec<PoolId>,
}

/// 流动性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquiditySnapshot {
    /// 总流动性价值（USD）
    pub total_value_usd: f64,
    /// 代币A储备
    pub token_a_reserve: TokenReserve,
    /// 代币B储备
    pub token_b_reserve: TokenReserve,
    /// 24小时交易量（USD）
    pub volume_24h_usd: f64,
    /// 年化收益率（APY）
    pub apy: Option<f64>,
    /// 费率（基点）
    pub fee_rate: u16,
}

/// 价格信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 当前价格（A/B）
    pub price: f64,
    /// 反向价格（B/A）
    pub inverse_price: f64,
    /// 24小时价格变化（百分比）
    pub price_change_24h: Option<f64>,
    /// 最后更新时间戳
    pub last_updated: u64,
}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// 总池数量
    pub total_pools: usize,
    /// 按协议分组的池数量
    pub pools_by_protocol: std::collections::HashMap<DexProtocol, usize>,
    /// 按类型分组的池数量
    pub pools_by_type: std::collections::HashMap<PoolType, usize>,
    /// 活跃池数量
    pub active_pools: usize,
    /// 总流动性价值（USD）
    pub total_liquidity_usd: f64,
    /// 最后更新时间
    pub last_updated: u64,
}

/// 更新事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpdateEvent {
    /// 池创建
    PoolCreated {
        pool_id: PoolId,
        timestamp: u64,
    },
    /// 池更新
    PoolUpdated {
        pool_id: PoolId,
        timestamp: u64,
        changes: Vec<String>,
    },
    /// 池删除
    PoolRemoved {
        pool_id: PoolId,
        timestamp: u64,
    },
    /// 交换事件
    SwapExecuted {
        pool_id: PoolId,
        input_token: Pubkey,
        output_token: Pubkey,
        input_amount: u64,
        output_amount: u64,
        timestamp: u64,
    },
}

/// 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PoolError {
    /// 池不存在
    PoolNotFound(PoolId),
    /// 不支持的操作
    UnsupportedOperation(String),
    /// 计算错误
    CalculationError(String),
    /// 数据解析错误
    ParseError(String),
    /// 网络错误
    NetworkError(String),
    /// 缓存错误
    CacheError(String),
}

impl fmt::Display for PoolError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PoolError::PoolNotFound(id) => write!(f, "Pool not found: {}", id),
            PoolError::UnsupportedOperation(op) => write!(f, "Unsupported operation: {}", op),
            PoolError::CalculationError(msg) => write!(f, "Calculation error: {}", msg),
            PoolError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            PoolError::NetworkError(msg) => write!(f, "Network error: {}", msg),
            PoolError::CacheError(msg) => write!(f, "Cache error: {}", msg),
        }
    }
}

impl std::error::Error for PoolError {}

/// 结果类型别名
pub type PoolResult<T> = Result<T>;
