//! 统一池管理器
//!
//! 提供跨 DEX 的统一池管理接口

use super::types::*;
use super::manager::*;
use super::pool::*;
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use shared::Result;

/// 统一池管理器
/// 管理所有 DEX 的所有池类型，提供统一的接口
#[derive(Debug)]
pub struct DexPoolManager {
    /// Raydium CLMM 管理器
    raydium_managers: Arc<RwLock<HashMap<Pubkey, crate::dex::raydium::clmm::RaydiumClmmPoolManager>>>,
    /// Meteora DLMM 管理器
    meteora_managers: Arc<RwLock<HashMap<Pubkey, crate::dex::meteora::dlmm::MeteoraLbPoolManager>>>,
    /// 配置
    config: DexPoolManagerConfig,
}

/// 统一管理器配置
#[derive(Debug, <PERSON><PERSON>)]
pub struct DexPoolManagerConfig {
    /// 缓存大小限制
    pub max_cache_size: usize,
    /// 缓存 TTL (毫秒)
    pub cache_ttl_ms: u64,
    /// 是否启用自动清理
    pub enable_auto_cleanup: bool,
    /// 清理间隔 (毫秒)
    pub cleanup_interval_ms: u64,
}

impl Default for DexPoolManagerConfig {
    fn default() -> Self {
        Self {
            max_cache_size: 10000,
            cache_ttl_ms: 300_000, // 5分钟
            enable_auto_cleanup: true,
            cleanup_interval_ms: 60_000, // 1分钟
        }
    }
}

impl DexPoolManager {
    /// 创建新的统一管理器
    pub fn new(config: DexPoolManagerConfig) -> Self {
        Self {
            raydium_managers: Arc::new(RwLock::new(HashMap::new())),
            meteora_managers: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    /// 使用默认配置创建
    pub fn with_default_config() -> Self {
        Self::new(DexPoolManagerConfig::default())
    }

    /// 添加 Raydium CLMM 池管理器
    pub fn add_raydium_pool(&self, manager: crate::dex::raydium::clmm::RaydiumClmmPoolManager) -> Result<()> {
        let pool_address = manager.get_state().pool_id;
        let mut managers = self.raydium_managers.write().unwrap();
        managers.insert(pool_address, manager);
        Ok(())
    }

    /// 添加 Meteora DLMM 池管理器
    pub fn add_meteora_pool(&self, manager: crate::dex::meteora::dlmm::MeteoraLbPoolManager) -> Result<()> {
        let pool_address = manager.pool_state.address;
        let mut managers = self.meteora_managers.write().unwrap();
        managers.insert(pool_address, manager);
        Ok(())
    }

    /// 估算交换 - 从所有支持的池中获取报价
    pub fn estimate_swap(
        &self,
        input_token: &Pubkey,
        output_token: &Pubkey,
        input_amount: u64,
        max_price_impact: Option<f64>,
    ) -> Result<Vec<SwapEstimation>> {
        let mut estimations = Vec::new();

        // 检查 Raydium CLMM 池
        let raydium_managers = self.raydium_managers.read().unwrap();
        for (pool_address, manager) in raydium_managers.iter() {
            let (token_0, token_1, _, _) = manager.get_state().token_pair();

            let direction = if token_0 == *input_token && token_1 == *output_token {
                SwapDirection::AToB
            } else if token_1 == *input_token && token_0 == *output_token {
                SwapDirection::BToA
            } else {
                continue; // 不匹配的代币对
            };

            if let Ok(estimation) = manager.estimate_swap_output(input_amount, direction, max_price_impact) {
                estimations.push(estimation);
            }
        }

        // 检查 Meteora DLMM 池
        let meteora_managers = self.meteora_managers.read().unwrap();
        for (pool_address, manager) in meteora_managers.iter() {
            let (token_x, token_y, _, _) = manager.pool_state.token_pair();

            let input_is_x = if token_x == *input_token && token_y == *output_token {
                true
            } else if token_y == *input_token && token_x == *output_token {
                false
            } else {
                continue; // 不匹配的代币对
            };

            if let Ok(estimation) = manager.estimate_swap_output(input_amount, input_is_x, max_price_impact) {
                estimations.push(estimation);
            }
        }

        Ok(estimations)
    }

    /// 获取最佳交换报价
    pub fn get_best_swap_quote(
        &self,
        input_token: &Pubkey,
        output_token: &Pubkey,
        input_amount: u64,
        max_price_impact: Option<f64>,
    ) -> Result<SwapEstimation> {
        let estimations = self.estimate_swap(input_token, output_token, input_amount, max_price_impact)?;

        // 选择输出最多的报价
        estimations
            .into_iter()
            .max_by_key(|est| est.output_amount)
            .ok_or_else(|| shared::EchoesError::NotFound(
                format!("No pools found for token pair {}/{}", input_token, output_token)
            ))
    }

    /// 查找支持指定代币对的所有池
    pub fn find_pools_for_tokens(
        &self,
        token_a: &Pubkey,
        token_b: &Pubkey,
    ) -> Vec<PoolId> {
        let mut pool_ids = Vec::new();

        // 检查 Raydium CLMM 池
        let raydium_managers = self.raydium_managers.read().unwrap();
        for (pool_address, manager) in raydium_managers.iter() {
            let (token_0, token_1, _, _) = manager.get_state().token_pair();
            if (token_0 == *token_a && token_1 == *token_b) || (token_1 == *token_a && token_0 == *token_b) {
                pool_ids.push(PoolId::new(DexProtocol::Raydium, PoolType::Clmm, *pool_address));
            }
        }

        // 检查 Meteora DLMM 池
        let meteora_managers = self.meteora_managers.read().unwrap();
        for (pool_address, manager) in meteora_managers.iter() {
            let (token_x, token_y, _, _) = manager.pool_state.token_pair();
            if (token_x == *token_a && token_y == *token_b) || (token_y == *token_a && token_x == *token_b) {
                pool_ids.push(PoolId::new(DexProtocol::Meteora, PoolType::Dlmm, *pool_address));
            }
        }

        pool_ids
    }

    /// 按协议获取池
    pub fn get_pools_by_protocol(&self, protocol: DexProtocol) -> Vec<PoolId> {
        let mut pool_ids = Vec::new();

        match protocol {
            DexProtocol::Raydium => {
                let raydium_managers = self.raydium_managers.read().unwrap();
                for pool_address in raydium_managers.keys() {
                    pool_ids.push(PoolId::new(DexProtocol::Raydium, PoolType::Clmm, *pool_address));
                }
            }
            DexProtocol::Meteora => {
                let meteora_managers = self.meteora_managers.read().unwrap();
                for pool_address in meteora_managers.keys() {
                    pool_ids.push(PoolId::new(DexProtocol::Meteora, PoolType::Dlmm, *pool_address));
                }
            }
            _ => {} // 其他协议暂不支持
        }

        pool_ids
    }

    /// 按池类型获取池
    pub fn get_pools_by_type(&self, pool_type: PoolType) -> Vec<PoolId> {
        let mut pool_ids = Vec::new();

        match pool_type {
            PoolType::Clmm => {
                let raydium_managers = self.raydium_managers.read().unwrap();
                for pool_address in raydium_managers.keys() {
                    pool_ids.push(PoolId::new(DexProtocol::Raydium, PoolType::Clmm, *pool_address));
                }
            }
            PoolType::Dlmm => {
                let meteora_managers = self.meteora_managers.read().unwrap();
                for pool_address in meteora_managers.keys() {
                    pool_ids.push(PoolId::new(DexProtocol::Meteora, PoolType::Dlmm, *pool_address));
                }
            }
            _ => {} // 其他类型暂不支持
        }

        pool_ids
    }

    /// 获取所有池的统计信息
    pub fn get_global_stats(&self) -> GlobalStats {
        let raydium_managers = self.raydium_managers.read().unwrap();
        let meteora_managers = self.meteora_managers.read().unwrap();

        let mut pools_by_protocol = HashMap::new();
        let mut pools_by_type = HashMap::new();

        // 统计 Raydium 池
        let raydium_count = raydium_managers.len();
        if raydium_count > 0 {
            pools_by_protocol.insert(DexProtocol::Raydium, raydium_count);
            pools_by_type.insert(PoolType::Clmm, raydium_count);
        }

        // 统计 Meteora 池
        let meteora_count = meteora_managers.len();
        if meteora_count > 0 {
            pools_by_protocol.insert(DexProtocol::Meteora, meteora_count);
            pools_by_type.insert(PoolType::Dlmm, meteora_count);
        }

        let total_pools = raydium_count + meteora_count;
        let active_pools = raydium_managers.values().filter(|m| m.get_state().is_active()).count()
            + meteora_managers.values().filter(|m| m.pool_state.is_active()).count();

        GlobalStats {
            total_pools,
            pools_by_protocol,
            pools_by_type,
            active_pools,
            total_liquidity_usd: 0.0, // 需要价格数据才能计算
            cache_size: total_pools,
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }

    /// 获取流动性最高的池
    pub fn get_top_pools_by_liquidity(&self, limit: usize) -> Vec<(PoolId, u128)> {
        let mut pools_with_liquidity = Vec::new();

        // 收集 Raydium 池的流动性
        let raydium_managers = self.raydium_managers.read().unwrap();
        for (pool_address, manager) in raydium_managers.iter() {
            let pool_id = PoolId::new(DexProtocol::Raydium, PoolType::Clmm, *pool_address);
            let liquidity = manager.get_state().liquidity;
            pools_with_liquidity.push((pool_id, liquidity));
        }

        // 收集 Meteora 池的流动性
        let meteora_managers = self.meteora_managers.read().unwrap();
        for (pool_address, manager) in meteora_managers.iter() {
            let pool_id = PoolId::new(DexProtocol::Meteora, PoolType::Dlmm, *pool_address);
            let liquidity = manager.pool_state.get_total_liquidity();
            pools_with_liquidity.push((pool_id, liquidity));
        }

        // 按流动性排序并返回前 N 个
        pools_with_liquidity.sort_by(|a, b| b.1.cmp(&a.1));
        pools_with_liquidity.into_iter().take(limit).collect()
    }

    /// 移除池
    pub fn remove_raydium_pool(&self, pool_address: &Pubkey) -> Option<crate::dex::raydium::clmm::RaydiumClmmPoolManager> {
        let mut managers = self.raydium_managers.write().unwrap();
        managers.remove(pool_address)
    }

    /// 移除 Meteora 池
    pub fn remove_meteora_pool(&self, pool_address: &Pubkey) -> Option<crate::dex::meteora::dlmm::MeteoraLbPoolManager> {
        let mut managers = self.meteora_managers.write().unwrap();
        managers.remove(pool_address)
    }

    /// 获取配置
    pub fn get_config(&self) -> &DexPoolManagerConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: DexPoolManagerConfig) {
        self.config = config;
    }
}

/// 全局统计信息
#[derive(Debug, Clone)]
pub struct GlobalStats {
    /// 总池数量
    pub total_pools: usize,
    /// 按协议分组的池数量
    pub pools_by_protocol: HashMap<DexProtocol, usize>,
    /// 按类型分组的池数量
    pub pools_by_type: HashMap<PoolType, usize>,
    /// 活跃池数量
    pub active_pools: usize,
    /// 总流动性价值（USD）
    pub total_liquidity_usd: f64,
    /// 缓存大小
    pub cache_size: usize,
    /// 最后更新时间
    pub last_updated: u64,
}
