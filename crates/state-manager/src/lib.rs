//! # State Manager
//!
//! 多 DEX 流动性池状态管理系统
//!
//! 本模块提供了一个统一的接口来管理不同 DEX 协议的流动性池状态，
//! 包括 Raydium、Meteora、Orca 等主要 DEX 的各种池类型。
//!

// 核心模块
pub mod core;
pub mod utils;

// DEX 特定实现
pub mod dex;

// 向后兼容的模块（逐步迁移）
// pub mod pool;
// pub mod snapshot;
// pub mod updater;
// pub mod raydium_clmm;
// pub mod meteora_dlmm;

// 重新导出核心类型和功能
pub use core::types::*;
pub use core::pool::PoolState as CorePoolState;
pub use core::manager::PoolManager as CorePoolManager;
pub use core::dex_manager::*;

// 重新导出 DEX 实现
pub use dex::*;

// 重新导出工具
pub use utils::*;

// 向后兼容导出（保留原有API）
// pub use pool::*;
// pub use snapshot::*;
// pub use updater::*;
// pub use raydium_clmm::*;
// pub use meteora_dlmm::*;
