//! Meteora DLMM 池管理器实现
//!
//! 实现 Meteora DLMM 特定的池管理逻辑

use super::types::*;
use super::types::utils;
use super::pool::MeteoraLbPairState;
use crate::core::{PoolManager, SwapDirection, SwapEstimation, LiquidityInfo};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use shared::{EchoesError, Result};

/// Meteora DLMM 池管理器
/// 负责管理DLMM池的状态和计算交换
#[derive(Debug, Clone)]
pub struct MeteoraLbPoolManager {
    /// 池状态
    pub pool_state: MeteoraLbPairState,
}

impl MeteoraLbPoolManager {
    /// 安全地计算 bin ID，避免溢出
    fn calculate_bin_id(&self, array_index: i64, bin_index: usize) -> Result<i32> {
        use super::types::math_constants::{MAX_BIN_ID, MIN_BIN_ID, MAX_BIN_PER_ARRAY};

        // 检查 bin_index 是否在有效范围内
        if bin_index >= MAX_BIN_PER_ARRAY {
            return Err(EchoesError::InvalidInput(
                format!("bin_index {} exceeds maximum {}", bin_index, MAX_BIN_PER_ARRAY)
            ));
        }

        // 计算 bin_id，检查溢出
        let bin_id_i64 = array_index
            .checked_mul(MAX_BIN_PER_ARRAY as i64)
            .and_then(|val| val.checked_add(bin_index as i64))
            .ok_or_else(|| EchoesError::InvalidState("bin ID calculation overflow".to_string()))?;

        // 检查 bin_id 是否在有效范围内
        if bin_id_i64 < MIN_BIN_ID as i64 || bin_id_i64 > MAX_BIN_ID as i64 {
            return Err(EchoesError::InvalidState(
                format!("bin_id {} is out of range [{}, {}]", bin_id_i64, MIN_BIN_ID, MAX_BIN_ID)
            ));
        }

        Ok(bin_id_i64 as i32)
    }
    /// 创建新的池管理器
    pub fn new(pool_state: MeteoraLbPairState) -> Self {
        Self { pool_state }
    }

    /// 从JSON数据创建池管理器
    pub fn from_json_data(
        address: Pubkey,
        lb_pair_data: &str,
        bin_arrays_data: &[&str],
        bitmap_data: Option<&str>,
    ) -> Result<Self> {
        // 解析LB Pair数据
        let lb_pair_json: serde_json::Value = serde_json::from_str(lb_pair_data)
            .map_err(|e| EchoesError::Parse(format!("Failed to parse LB pair data: {}", e)))?;

        // 提取基本信息（这里需要根据实际JSON结构调整）
        let parsed_data = &lb_pair_json["parsed"]["data"];

        // 创建基础池状态
        let mut pool_state = MeteoraLbPairState::new(
            address,
            Pubkey::from_str("11111111111111111111111111111111").unwrap(), // 占位符
            Pubkey::from_str("11111111111111111111111111111111").unwrap(), // 占位符
            6, // 默认精度
            9, // 默认精度
            0, // 默认活跃ID
            1, // 默认bin步长
        );

        // 解析bin数组数据
        for bin_array_data in bin_arrays_data {
            let bin_array_json: serde_json::Value = serde_json::from_str(bin_array_data)
                .map_err(|e| EchoesError::Parse(format!("Failed to parse bin array data: {}", e)))?;

            let parsed_bin_data = &bin_array_json["parsed"]["data"];
            let array_index = parsed_bin_data["index"].as_str()
                .and_then(|s| s.parse::<i64>().ok())
                .unwrap_or(0);

            let lb_pair_str = parsed_bin_data["lbPair"].as_str().unwrap_or("");
            let lb_pair = Pubkey::from_str(lb_pair_str)
                .unwrap_or_else(|_| Pubkey::from_str("11111111111111111111111111111111").unwrap());

            let mut bins = Vec::new();
            if let Some(bins_array) = parsed_bin_data["bins"].as_array() {
                for bin_data in bins_array {
                    let bin = MeteoraLbBin {
                        amount_x: bin_data["amountX"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        amount_y: bin_data["amountY"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        price: bin_data["price"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        liquidity_supply: bin_data["liquiditySupply"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        reward_per_token_stored: [
                            bin_data["rewardPerTokenStored"][0].as_str()
                                .and_then(|s| s.parse().ok()).unwrap_or(0),
                            bin_data["rewardPerTokenStored"][1].as_str()
                                .and_then(|s| s.parse().ok()).unwrap_or(0),
                        ],
                        fee_amount_x_per_token_stored: bin_data["feeAmountXPerTokenStored"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        fee_amount_y_per_token_stored: bin_data["feeAmountYPerTokenStored"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        amount_x_in: bin_data["amountXIn"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        amount_y_in: bin_data["amountYIn"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                    };
                    bins.push(bin);
                }
            }

            let bin_array = MeteoraLbBinArray {
                index: array_index,
                version: parsed_bin_data["version"].as_u64().unwrap_or(1) as u8,
                padding: [0; 7],
                lb_pair,
                bins,
            };

            pool_state.update_bin_array(array_index, bin_array);
        }

        // 解析位图数据（如果提供）
        if let Some(bitmap_data) = bitmap_data {
            let bitmap_json: serde_json::Value = serde_json::from_str(bitmap_data)
                .map_err(|e| EchoesError::Parse(format!("Failed to parse bitmap data: {}", e)))?;

            let parsed_bitmap_data = &bitmap_json["parsed"]["data"];
            let lb_pair_str = parsed_bitmap_data["lbPair"].as_str().unwrap_or("");
            let lb_pair = Pubkey::from_str(lb_pair_str)
                .unwrap_or_else(|_| Pubkey::from_str("11111111111111111111111111111111").unwrap());

            // 解析位图数组（简化处理）
            let positive_bitmap = [[0u64; 8]; 12];
            let negative_bitmap = [[0u64; 8]; 12];

            let bitmap_extension = MeteoraLbBinArrayBitmapExtension {
                lb_pair,
                positive_bin_array_bitmap: positive_bitmap,
                negative_bin_array_bitmap: negative_bitmap,
            };

            pool_state.bitmap_extension = Some(bitmap_extension);
        }

        Ok(Self::new(pool_state))
    }

    /// 估算交换输出
    /// 基于 Meteora DLMM SDK 的实现逻辑，提供更准确的交换估算
    pub fn estimate_swap_output(
        &self,
        input_amount: u64,
        input_is_x: bool,
        max_price_impact: Option<f64>,
    ) -> Result<SwapEstimation> {
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput("Input amount cannot be zero".to_string()));
        }

        // 创建池状态的可修改副本用于模拟交换
        let mut pool_state_copy = self.pool_state.clone();
        let mut remaining_input = input_amount;
        let mut total_output = 0u64;
        let mut total_fee = 0u64;
        let mut total_protocol_fee = 0u64;
        let mut total_lp_fee = 0u64;
        let mut bins_used = Vec::new();
        let mut current_bin_id = self.pool_state.active_id;

        // 更新波动性参数和参考值
        let current_timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;
        let _ = pool_state_copy.update_references(current_timestamp);

        let initial_price = self.pool_state.get_active_bin_price()?;
        let mut final_price = initial_price;

        // 遍历限制，防止无限循环
        let mut bin_traversal_count = 0;
        const MAX_BIN_TRAVERSALS: usize = 20;

        println!("Starting swap: input_amount: {}, input_is_x: {}, active_bin_id: {}", input_amount, input_is_x, current_bin_id);

        // 基于 SDK 逻辑的精确交换计算
        while remaining_input > 0 && bin_traversal_count < MAX_BIN_TRAVERSALS {
            bin_traversal_count += 1;

            // 从副本中获取当前 bin 的数据
            let bin_data = self.get_bin_by_id_from_state(&pool_state_copy, current_bin_id);

            if let Some(bin) = bin_data.cloned() {
                // 检查bin是否有足够的流动性
                let available_liquidity = if input_is_x { bin.amount_y } else { bin.amount_x };
                if available_liquidity == 0 {
                    println!("Bin {} has no liquidity for swap direction, moving to next bin", current_bin_id);
                    // 尝试移动到下一个bin
                    match self.get_next_active_bin_id(current_bin_id, input_is_x) {
                        Ok(next_bin_id) => {
                            current_bin_id = next_bin_id;
                            continue;
                        },
                        Err(_) => break,
                    }
                }
                // 计算当前 bin 的精确价格
                let bin_price = self.pool_state.get_bin_price(current_bin_id)?;

                println!("Processing bin {}: price: {}, amount_x: {}, amount_y: {}",
                    current_bin_id, bin_price, bin.amount_x, bin.amount_y);

                // 更新波动性累加器
                let _ = pool_state_copy.update_volatility_accumulator();

                // 使用bin的swap方法进行精确计算
                let swap_result = bin.swap(remaining_input as u128, bin_price, input_is_x, &pool_state_copy)?;

                // 更新累计值
                let consumed_input_with_fees = swap_result.amount_in_with_fees as u64;
                let consumed_input = swap_result.amount_in_consumed as u64;
                let output_amount = swap_result.amount_out as u64;
                let fee_amount = swap_result.fee;
                let protocol_fee_amount = swap_result.protocol_fee;
                let lp_fee_amount = swap_result.lp_fee;

                remaining_input = remaining_input.saturating_sub(consumed_input_with_fees);
                total_output = total_output.saturating_add(output_amount);
                total_fee = total_fee.saturating_add(fee_amount);
                total_protocol_fee = total_protocol_fee.saturating_add(protocol_fee_amount);
                total_lp_fee = total_lp_fee.saturating_add(lp_fee_amount);

                // 记录使用的bin信息
                bins_used.push(BinUsage {
                    bin_id: current_bin_id,
                    input_amount: consumed_input_with_fees,
                    output_amount,
                    price: bin_price,
                    fee: fee_amount,
                });

                // 更新池状态副本中的bin
                let mut updated_bin = bin.clone();
                if input_is_x {
                    updated_bin.amount_y = updated_bin.amount_y.saturating_sub(output_amount);
                    updated_bin.amount_x = updated_bin.amount_x.saturating_add(consumed_input);
                } else {
                    updated_bin.amount_x = updated_bin.amount_x.saturating_sub(output_amount);
                    updated_bin.amount_y = updated_bin.amount_y.saturating_add(consumed_input);
                }

                // 更新 bin 中的费用统计
                if input_is_x {
                    updated_bin.amount_x_in = updated_bin.amount_x_in.saturating_add(consumed_input);
                } else {
                    updated_bin.amount_y_in = updated_bin.amount_y_in.saturating_add(consumed_input);
                }

                // 更新状态副本
                if let Err(e) = self.update_bin_in_state(&mut pool_state_copy, current_bin_id, updated_bin) {
                    println!("警告: 无法更新bin状态: {}", e);
                }

                // 更新最终价格
                final_price = bin_price;

                println!("swap result - bin_id: {}, input: {}, output: {}, fee: {}, remaining: {}",
                    current_bin_id, consumed_input, output_amount, fee_amount, remaining_input);

                // 如果这个bin被完全耗尽或输入已用完，继续到下一个bin
                if output_amount == 0 || consumed_input_with_fees == 0 || remaining_input == 0 {
                    if remaining_input > 0 {
                        // 还有剩余输入，尝试下一个bin
                        match self.get_next_active_bin_id(current_bin_id, input_is_x) {
                            Ok(next_bin_id) => {
                                current_bin_id = next_bin_id;
                                continue;
                            },
                            Err(_) => break,
                        }
                    } else {
                        break;
                    }
                }
            } else {
                println!("Bin {} not found, attempting to find next active bin", current_bin_id);
                // 当前bin不存在，尝试找到下一个活跃bin
                match self.get_next_active_bin_id(current_bin_id, input_is_x) {
                    Ok(next_bin_id) => {
                        println!("Found next active bin: {}", next_bin_id);
                        current_bin_id = next_bin_id;
                    },
                    Err(_) => {
                        println!("No more active bins found, stopping swap");
                        break;
                    }
                }
            }
        }

        println!("Swap completed: total_output: {}, total_fee: {}, remaining_input: {}",
            total_output, total_fee, remaining_input);

        // 计算精确的价格影响
        let price_impact = self.calculate_precise_price_impact(initial_price, final_price, input_is_x)?;

        // 检查价格影响限制
        if let Some(max_impact) = max_price_impact {
            if price_impact > max_impact {
                return Err(EchoesError::InvalidState(
                    format!("Price impact {:.2}% exceeds maximum {:.2}%", price_impact, max_impact)
                ));
            }
        }

        // 检查是否有任何输出产生
        if total_output == 0 && remaining_input > 0 {
            return Err(EchoesError::InvalidState(
                "No output generated despite remaining input - insufficient liquidity".to_string()
            ));
        }

        // 检查输出的合理性
        if total_output > u64::MAX / 2 {
            return Err(EchoesError::InvalidState(
                "Output amount too large - potential calculation overflow".to_string()
            ));
        }

        Ok(SwapEstimation {
            input_amount,
            output_amount: total_output,
            fee_amount: total_fee,
            protocol_fee_amount: total_protocol_fee,
            lp_fee_amount: total_lp_fee,
            price_impact,
            bins_used,
            remaining_input,
        })
    }


    /// 根据bin ID获取bin数据
    fn get_bin_by_id(&self, bin_id: i32) -> Option<&MeteoraLbBin> {
        self.get_bin_by_id_from_state(&self.pool_state, bin_id)
    }

    /// 从指定状态中根据bin ID获取bin数据
    fn get_bin_by_id_from_state<'a>(&self, state: &'a MeteoraLbPairState, bin_id: i32) -> Option<&'a MeteoraLbBin> {
        let bin_id_64 = bin_id as i64;
        let max_bin_per_array = math_constants::MAX_BIN_PER_ARRAY as i64;

        // 计算 array_index：使用欧几里得除法确保与其它语言一致的行为
        let array_index = if bin_id_64 < 0 && bin_id_64 % max_bin_per_array != 0 {
            bin_id_64 / max_bin_per_array - 1
        } else {
            bin_id_64 / max_bin_per_array
        };

        // 计算 bin_index：使用 rem_euclid 确保结果在 [0, MAX_BIN_PER_ARRAY) 范围内
        let bin_index = bin_id_64.rem_euclid(max_bin_per_array) as usize;

        if let Some(bin_array) = state.get_bin_array(array_index) {
            // 直接使用 bin_index，不加1
            bin_array.get_bin(bin_index)
        } else {
            None
        }
    }

    /// 更新池状态副本中的 bin 数据
    fn update_bin_in_state(&self, state: &mut MeteoraLbPairState, bin_id: i32, updated_bin: MeteoraLbBin) -> Result<()> {
        let bin_id_64 = bin_id as i64;
        let max_bin_per_array = math_constants::MAX_BIN_PER_ARRAY as i64;

        // 使用与get_bin_by_id_from_state相同的逻辑计算索引
        let array_index = if bin_id_64 < 0 && bin_id_64 % max_bin_per_array != 0 {
            bin_id_64 / max_bin_per_array - 1
        } else {
            bin_id_64 / max_bin_per_array
        };
        let bin_index = bin_id_64.rem_euclid(max_bin_per_array) as usize;

        // 检查bin_index是否在有效范围内
        if bin_index >= math_constants::MAX_BIN_PER_ARRAY {
            return Err(EchoesError::InvalidInput(
                format!("bin_index {} exceeds maximum {}", bin_index, math_constants::MAX_BIN_PER_ARRAY)
            ));
        }

        // 获取可变引用的bin数组并更新
        if let Some(bin_array) = state.bin_arrays.get_mut(&array_index) {
            if bin_index < bin_array.bins.len() {
                // 更新指定 bin 的数据
                bin_array.bins[bin_index] = updated_bin;
                Ok(())
            } else {
                Err(EchoesError::InvalidState(format!("Bin index {} out of bounds for array {}", bin_index, array_index)))
            }
        } else {
            Err(EchoesError::InvalidState(format!("Bin array {} not found", array_index)))
        }
    }

    /// 获取下一个有流动性的活跃 bin ID（基于位图优化）
    fn get_next_active_bin_id(&self, current_bin_id: i32, input_is_x: bool) -> Result<i32> {
        if input_is_x {
            // X -> Y: 向更低的bin ID移动（价格下降）
            self.find_next_lower_active_bin(current_bin_id)
        } else {
            // Y -> X: 向更高的bin ID移动（价格上升）
            self.find_next_higher_active_bin(current_bin_id)
        }
    }

    /// 查找下一个有Y流动性的更低活跃bin ID
    fn find_next_lower_active_bin(&self, start_bin_id: i32) -> Result<i32> {
        // 使用位图扩展优化搜索（如果可用）
        if let Some(bitmap) = &self.pool_state.bitmap_extension {
            let active_arrays = bitmap.get_active_bin_array_indices();

            // 查找包含目标 bin 范围的活跃数组
            for &array_index in active_arrays.iter().rev() {
                let array_start_bin = array_index * math_constants::MAX_BIN_PER_ARRAY as i64;
                let array_end_bin = array_start_bin + math_constants::MAX_BIN_PER_ARRAY as i64 - 1;

                // 如果数组范围在目标范围内
                if array_end_bin < start_bin_id as i64 {
                    if let Some(bin_array) = self.pool_state.get_bin_array(array_index) {
                        // 从高到低检查这个数组中的 bins
                        for bin_index in (0..bin_array.bins.len()).rev() {
                            let bin_id = array_start_bin + bin_index as i64;
                            if bin_id < start_bin_id as i64 {
                                let bin = &bin_array.bins[bin_index];
                                if bin.amount_y > 0 {
                                    return Ok(bin_id as i32);
                                }
                            }
                        }
                    }
                }
            }
        }

        println!("开始线性搜索");
        // 回退到线性搜索
        for bin_id in (math_constants::MIN_BIN_ID..start_bin_id).rev() {
            println!("Checking bin {}", bin_id);
            if let Some(bin) = self.get_bin_by_id(bin_id) {
                if bin.amount_y > 0 {
                    return Ok(bin_id);
                }
            }
        }

        Err(EchoesError::InvalidState("No lower bin with Y liquidity found".to_string()))
    }

    /// 查找下一个有X流动性的更高活跃bin ID
    fn find_next_higher_active_bin(&self, start_bin_id: i32) -> Result<i32> {
        // 使用位图扩展优化搜索（如果可用）
        if let Some(bitmap) = &self.pool_state.bitmap_extension {
            let active_arrays = bitmap.get_active_bin_array_indices();

            // 查找包含目标 bin 范围的活跃数组
            for &array_index in active_arrays.iter() {
                let array_start_bin = array_index * math_constants::MAX_BIN_PER_ARRAY as i64;
                let array_end_bin = array_start_bin + math_constants::MAX_BIN_PER_ARRAY as i64 - 1;

                // 如果数组范围在目标范围内
                if array_start_bin > start_bin_id as i64 {
                    if let Some(bin_array) = self.pool_state.get_bin_array(array_index) {
                        // 从低到高检查这个数组中的 bins
                        for (bin_index, bin) in bin_array.bins.iter().enumerate() {
                            let bin_id = array_start_bin + bin_index as i64;
                            if bin_id > start_bin_id as i64 && bin.amount_x > 0 {
                                return Ok(bin_id as i32);
                            }
                        }
                    }
                }
            }
        }

        // 回退到线性搜索
        for bin_id in (start_bin_id + 1)..=math_constants::MAX_BIN_ID {
            if let Some(bin) = self.get_bin_by_id(bin_id) {
                if bin.amount_x > 0 {
                    return Ok(bin_id);
                }
            }
        }

        Err(EchoesError::InvalidState("No higher bin with X liquidity found".to_string()))
    }



    /// 计算精确的价格影响
    fn calculate_precise_price_impact(&self, initial_price: u128, final_price: u128, input_is_x: bool) -> Result<f64> {
        if initial_price == 0 {
            return Ok(0.0);
        }

        if initial_price == final_price {
            return Ok(0.0);
        }

        // 转换为浮点数，保持更高精度
        let initial_float = utils::q64_to_float(initial_price);
        let final_float = utils::q64_to_float(final_price);

        // 计算价格影响
        let price_change_ratio = if input_is_x {
            // X -> Y 交换：价格应该下降（从初始价格到最终价格）
            if final_float < initial_float {
                (initial_float - final_float) / initial_float
            } else {
                // 异常情况：价格上升了
                (final_float - initial_float) / initial_float
            }
        } else {
            // Y -> X 交换：价格应该上升
            if final_float > initial_float {
                (final_float - initial_float) / initial_float
            } else {
                // 异常情况：价格下降了
                (initial_float - final_float) / initial_float
            }
        };

        // 转换为百分比
        Ok(price_change_ratio * 100.0)
    }

    /// 获取当前价格
    pub fn get_current_price(&self) -> Result<f64> {
        let price_q64 = self.pool_state.get_active_bin_price()?;
        let price_float = utils::q64_to_float(price_q64);

        // 调整小数位差异
        let decimals_diff = self.pool_state.token_y_decimals as i32 - self.pool_state.token_x_decimals as i32;
        let adjusted_price = price_float * 10f64.powi(decimals_diff);

        Ok(adjusted_price)
    }

    /// 更新池状态
    pub fn update_pool_state(&mut self, new_state: MeteoraLbPairState) {
        self.pool_state = new_state;
    }

    /// 获取流动性分布信息
    pub fn get_liquidity_info(&self) -> LiquidityInfo {
        let distribution = self.pool_state.get_liquidity_distribution();
        let total_liquidity = self.pool_state.get_total_liquidity();
        let active_bin_count = distribution.len();

        let (min_price, max_price) = if distribution.is_empty() {
            (0, 0)
        } else {
            let min_q64 = distribution.iter().map(|(_, price, _)| *price).min().unwrap_or(0);
            let max_q64 = distribution.iter().map(|(_, price, _)| *price).max().unwrap_or(0);

            // 转换 Q64.64 格式并调整精度差异
            let decimals_diff = self.pool_state.token_y_decimals as i32 - self.pool_state.token_x_decimals as i32;
            let adjustment_factor = 10f64.powi(decimals_diff);

            // let min_adjusted = ((min_q64 as f64 / (1u128 << 64) as f64) * adjustment_factor) as u128;
            // let max_adjusted = ((max_q64 as f64 / (1u128 << 64) as f64) * adjustment_factor) as u128;

            let min_adjusted = (min_q64 as f64 * adjustment_factor) as u128;
            let max_adjusted = (max_q64 as f64 * adjustment_factor) as u128;

            (min_adjusted, max_adjusted)
        };

        LiquidityInfo {
            total_liquidity,
            active_bin_count,
            price_range: (min_price, max_price),
            distribution,
        }
    }
}

/// Meteora DLMM 池状态缓存管理器
#[derive(Debug)]
pub struct MeteoraLbPoolCache {
    /// 池管理器映射
    pools: std::sync::Arc<std::sync::RwLock<std::collections::HashMap<Pubkey, MeteoraLbPoolManager>>>,
}

impl Default for MeteoraLbPoolCache {
    fn default() -> Self {
        Self::new()
    }
}

impl MeteoraLbPoolCache {
    /// 创建新的缓存管理器
    pub fn new() -> Self {
        Self {
            pools: std::sync::Arc::new(std::sync::RwLock::new(std::collections::HashMap::new())),
        }
    }

    /// 添加或更新池
    pub fn upsert_pool(&self, address: Pubkey, manager: MeteoraLbPoolManager) -> Result<()> {
        let mut pools = self.pools.write().unwrap();
        pools.insert(address, manager);
        Ok(())
    }

    /// 获取池管理器
    pub fn get_pool(&self, address: &Pubkey) -> Option<MeteoraLbPoolManager> {
        let pools = self.pools.read().unwrap();
        pools.get(address).cloned()
    }

    /// 移除池
    pub fn remove_pool(&self, address: &Pubkey) -> Option<MeteoraLbPoolManager> {
        let mut pools = self.pools.write().unwrap();
        pools.remove(address)
    }

    /// 获取所有池地址
    pub fn get_all_pool_addresses(&self) -> Vec<Pubkey> {
        let pools = self.pools.read().unwrap();
        pools.keys().cloned().collect()
    }

    /// 根据代币对查找池
    pub fn find_pools_by_tokens(&self, token_x: &Pubkey, token_y: &Pubkey) -> Vec<(Pubkey, MeteoraLbPoolManager)> {
        let pools = self.pools.read().unwrap();
        pools
            .iter()
            .filter(|(_, manager)| {
                let state = &manager.pool_state;
                (state.token_x_mint == *token_x && state.token_y_mint == *token_y) ||
                (state.token_x_mint == *token_y && state.token_y_mint == *token_x)
            })
            .map(|(&addr, manager)| (addr, manager.clone()))
            .collect()
    }

    /// 清空缓存
    pub fn clear(&self) {
        let mut pools = self.pools.write().unwrap();
        pools.clear();
    }

    /// 获取缓存统计
    pub fn get_stats(&self) -> CacheStats {
        let pools = self.pools.read().unwrap();
        let total_pools = pools.len();
        let total_liquidity: u128 = pools.values()
            .map(|manager| manager.pool_state.get_total_liquidity())
            .sum();

        CacheStats {
            total_pools,
            total_liquidity,
            active_pools: pools.values()
                .filter(|manager| manager.pool_state.get_total_liquidity() > 0)
                .count(),
        }
    }
}

/// 实现 PoolManager trait for MeteoraLbPoolManager
impl PoolManager for MeteoraLbPoolManager {
    type State = MeteoraLbPairState;

    fn get_state(&self) -> &Self::State {
        &self.pool_state
    }

    fn get_current_price(&self) -> Result<f64> {
        self.pool_state.current_price()
    }

    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        max_price_impact: Option<f64>,
    ) -> Result<SwapEstimation> {
        // 转换方向参数
        let input_is_x = match direction {
            SwapDirection::AToB => true,  // X -> Y
            SwapDirection::BToA => false, // Y -> X
        };

        // 调用现有的估算方法
        let swap_estimation = self.estimate_swap_output(input_amount, input_is_x, max_price_impact)?;

        // 已经是 SwapEstimation 格式，直接返回
        Ok(swap_estimation)
    }

    fn get_liquidity_info(&self) -> LiquidityInfo {
        self.get_liquidity_info()
    }

    fn update_pool_state(&mut self, new_state: Self::State) -> Result<()> {
        self.pool_state = new_state;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_bin_by_id_bin_index_calculation() {
        // 测试 bin_index 计算的正确性，特别是对负数 bin_id 的处理
        // 使用向上取整逻辑
        let max_bin_per_array = math_constants::MAX_BIN_PER_ARRAY as i64; // 70

        // 测试正数和负数 bin_id 的向上取整逻辑
        let test_cases = vec![
            // (bin_id, expected_array_index, expected_bin_index)
            // 基本整数除法测试
            (0, 0, 0),      // 0 / 70 = 0, 0 % 70 = 0
            (1, 0, 1),      // 1 / 70 = 0, 1 % 70 = 1
            (69, 0, 69),    // 69 / 70 = 0, 69 % 70 = 69
            (70, 1, 0),     // 70 / 70 = 1, 70 % 70 = 0
            (71, 1, 1),     // 71 / 70 = 1, 71 % 70 = 1
            (139, 1, 69),   // 139 / 70 = 1, 139 % 70 = 69
            (140, 2, 0),    // 140 / 70 = 2, 140 % 70 = 0

            // 负数测试 - 使用 rem_euclid 确保 bin_index 非负
            (-1, -1, 69),   // -1 / 70 = -1, (-1).rem_euclid(70) = 69
            (-2, -1, 68),   // -2 / 70 = -1, (-2).rem_euclid(70) = 68
            (-69, -1, 1),   // -69 / 70 = -1, (-69).rem_euclid(70) = 1
            (-70, -1, 0),   // -70 / 70 = -1, (-70).rem_euclid(70) = 0
            (-71, -2, 69),  // -71 / 70 = -2, (-71).rem_euclid(70) = 69
            (-140, -2, 0),  // -140 / 70 = -2, (-140).rem_euclid(70) = 0
            (-141, -3, 69), // -141 / 70 = -3, (-141).rem_euclid(70) = 69
        ];

        for (bin_id, expected_array_index, expected_bin_index) in test_cases {
            let bin_id_64 = bin_id as i64;

            // 使用与实现中相同的逻辑计算 array_index
            let array_index = if bin_id_64 < 0 && bin_id_64 % max_bin_per_array != 0 {
                bin_id_64 / max_bin_per_array - 1
            } else {
                bin_id_64 / max_bin_per_array
            };
            let bin_index = bin_id_64.rem_euclid(max_bin_per_array) as usize;

            assert_eq!(
                array_index, expected_array_index,
                "bin_id: {}, expected array_index: {}, got: {}",
                bin_id, expected_array_index, array_index
            );
            assert_eq!(
                bin_index, expected_bin_index,
                "bin_id: {}, expected bin_index: {}, got: {}",
                bin_id, expected_bin_index, bin_index
            );

            // 验证 bin_index 始终在有效范围内
            assert!(bin_index < math_constants::MAX_BIN_PER_ARRAY,
                "bin_index {} should be < {}", bin_index, math_constants::MAX_BIN_PER_ARRAY);
        }
    }

    #[test]
    fn test_bin_id_roundtrip() {
        // 测试从 (array_index, bin_index) 计算 bin_id，然后再反向计算
        // 注意：由于使用了向上取整，完全的往返一致性不再适用
        // 这个测试主要验证 bin_index 的计算是否正确
        let max_bin_per_array = math_constants::MAX_BIN_PER_ARRAY as i64;

        let test_array_indices = vec![-3, -2, -1, 0, 1, 2, 3];

        for array_index in test_array_indices {
            for bin_index in 0..math_constants::MAX_BIN_PER_ARRAY {
                // 计算 bin_id
                let bin_id = array_index * max_bin_per_array + bin_index as i64;

                // 确保在有效范围内
                if bin_id < math_constants::MIN_BIN_ID as i64 || bin_id > math_constants::MAX_BIN_ID as i64 {
                    continue;
                }

                // 使用与实现中相同的逻辑反向计算
                let calculated_array_index = if bin_id < 0 && bin_id % max_bin_per_array != 0 {
                    bin_id / max_bin_per_array - 1
                } else {
                    bin_id / max_bin_per_array
                };
                let calculated_bin_index = bin_id.rem_euclid(max_bin_per_array) as usize;

                // bin_index 应该总是一致的
                assert_eq!(
                    calculated_bin_index, bin_index,
                    "bin_index mismatch for bin_id {}: expected {}, got {}",
                    bin_id, bin_index, calculated_bin_index
                );

                // array_index 应该总是一致的，因为我们使用的是简单的整数除法
                assert_eq!(calculated_array_index, array_index,
                    "array_index mismatch for bin_id {}: expected {}, got {}",
                    bin_id, array_index, calculated_array_index);
            }
        }
    }
}
